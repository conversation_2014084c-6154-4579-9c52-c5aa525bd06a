import React from 'react';
import { Edit, Trash2, Eye, Star, Users, Fuel, Settings } from 'lucide-react';
import { Car } from '../../types';

interface CarCardProps {
  car: Car;
  onEdit: (car: Car) => void;
  onDelete: (id: number) => void;
  onView: (car: Car) => void;
}

const CarCard: React.FC<CarCardProps> = ({ car, onEdit, onDelete, onView }) => {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'economy':
        return 'bg-green-100 text-green-800';
      case 'compact':
        return 'bg-blue-100 text-blue-800';
      case 'suv':
        return 'bg-orange-100 text-orange-800';
      case 'luxury':
        return 'bg-purple-100 text-purple-800';
      case 'sport':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'economy':
        return 'Économique';
      case 'compact':
        return 'Compact';
      case 'suv':
        return 'SUV';
      case 'luxury':
        return 'Luxe';
      case 'sport':
        return 'Sport';
      default:
        return category;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
      {/* Image */}
      <div className="relative h-48 bg-gray-200">
        <img
          src={car.image || 'https://via.placeholder.com/400x300?text=Car+Image'}
          alt={`${car.brand} ${car.model}`}
          className="w-full h-full object-cover"
        />
        <div className="absolute top-3 left-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(car.category)}`}>
            {getCategoryText(car.category)}
          </span>
        </div>
        <div className="absolute top-3 right-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            car.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {car.available ? 'Disponible' : 'Loué'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{car.brand} {car.model}</h3>
            <p className="text-sm text-gray-600">{car.year}</p>
          </div>
          <div className="text-right">
            <p className="text-xl font-bold text-blue-600">{car.price} TND</p>
            <p className="text-xs text-gray-500">par jour</p>
          </div>
        </div>

        {/* Features */}
        <div className="flex items-center space-x-4 mb-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Users className="w-4 h-4" />
            <span>{car.seats} places</span>
          </div>
          <div className="flex items-center space-x-1">
            <Settings className="w-4 h-4" />
            <span>{car.transmission === 'manual' ? 'Manuelle' : 'Automatique'}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Fuel className="w-4 h-4" />
            <span>{car.fuel === 'gasoline' ? 'Essence' : car.fuel === 'diesel' ? 'Diesel' : car.fuel === 'electric' ? 'Électrique' : 'Hybride'}</span>
          </div>
        </div>

        {/* Features list */}
        {car.features && car.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {car.features.slice(0, 3).map((feature, index) => (
                <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  {feature}
                </span>
              ))}
              {car.features.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  +{car.features.length - 3} autres
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <button
            onClick={() => onView(car)}
            className="flex items-center space-x-1 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
          >
            <Eye className="w-4 h-4" />
            <span className="text-sm">Voir</span>
          </button>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onEdit(car)}
              className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <Edit className="w-4 h-4" />
              <span className="text-sm">Modifier</span>
            </button>
            <button
              onClick={() => onDelete(car.id)}
              className="flex items-center space-x-1 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span className="text-sm">Supprimer</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CarCard;

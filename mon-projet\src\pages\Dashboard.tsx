import React from 'react';
import { 
  Car, 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Clock,
  MapPin,
  Star
} from 'lucide-react';
import StatsCard from '../components/Dashboard/StatsCard';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

const Dashboard: React.FC = () => {
  // Données simulées pour les graphiques
  const revenueData = [
    { month: 'Jan', revenue: 4000 },
    { month: 'Fév', revenue: 3000 },
    { month: 'Mar', revenue: 5000 },
    { month: 'Avr', revenue: 4500 },
    { month: 'Mai', revenue: 6000 },
    { month: 'Jun', revenue: 5500 },
  ];

  const carCategoryData = [
    { name: 'Économique', value: 35, color: '#3B82F6' },
    { name: 'Compact', value: 25, color: '#10B981' },
    { name: 'SUV', value: 20, color: '#F59E0B' },
    { name: 'Luxe', value: 15, color: '#EF4444' },
    { name: 'Sport', value: 5, color: '#8B5CF6' },
  ];

  const recentRentals = [
    { id: 1, client: 'Ahmed <PERSON> Ali', car: 'Toyota Corolla', date: '2024-01-15', status: 'active' },
    { id: 2, client: 'Fatma Trabelsi', car: 'BMW X3', date: '2024-01-14', status: 'completed' },
    { id: 3, client: 'Mohamed Gharbi', car: 'Mercedes C-Class', date: '2024-01-13', status: 'pending' },
    { id: 4, client: 'Leila Mansouri', car: 'Audi A4', date: '2024-01-12', status: 'active' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'En cours';
      case 'completed':
        return 'Terminée';
      case 'pending':
        return 'En attente';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Vue d'ensemble de votre activité</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Nouveau rapport
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Voitures"
          value="124"
          change="+12% ce mois"
          changeType="increase"
          icon={Car}
          color="bg-blue-500"
        />
        <StatsCard
          title="Clients Actifs"
          value="1,847"
          change="+8% ce mois"
          changeType="increase"
          icon={Users}
          color="bg-green-500"
        />
        <StatsCard
          title="Locations Actives"
          value="89"
          change="+23% ce mois"
          changeType="increase"
          icon={Calendar}
          color="bg-orange-500"
        />
        <StatsCard
          title="Revenus Mensuels"
          value="45,230 TND"
          change="+15% ce mois"
          changeType="increase"
          icon={DollarSign}
          color="bg-purple-500"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Revenus Mensuels</h3>
            <TrendingUp className="w-5 h-5 text-green-500" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3B82F6" 
                strokeWidth={3}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Car Categories Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Répartition par Catégorie</h3>
            <Star className="w-5 h-5 text-yellow-500" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={carCategoryData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {carCategoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Rentals */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Locations Récentes</h3>
            <Clock className="w-5 h-5 text-blue-500" />
          </div>
          <div className="space-y-4">
            {recentRentals.map((rental) => (
              <div key={rental.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{rental.client}</p>
                  <p className="text-sm text-gray-600">{rental.car}</p>
                  <p className="text-xs text-gray-500">{rental.date}</p>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                  {getStatusText(rental.status)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Actions Rapides</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center space-x-3 p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
              <Car className="w-5 h-5 text-blue-600" />
              <span className="text-blue-700 font-medium">Ajouter une voiture</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
              <Users className="w-5 h-5 text-green-600" />
              <span className="text-green-700 font-medium">Nouveau client</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 text-left bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
              <Calendar className="w-5 h-5 text-orange-600" />
              <span className="text-orange-700 font-medium">Nouvelle location</span>
            </button>
            <button className="w-full flex items-center space-x-3 p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
              <MapPin className="w-5 h-5 text-purple-600" />
              <span className="text-purple-700 font-medium">Gérer emplacements</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

import React, { useEffect, useState } from 'react';
import axios from 'axios';

// Définir le type d’un produit
type Product = {
  id: number;
  name: string;
  price: number;
};

function App() {
  const [products, setProducts] = useState<Product[]>([]);
  const [name, setName] = useState('');
  const [price, setPrice] = useState('');

  const fetchProducts = () => {
    axios.get('http://localhost:4000/products')
      .then(res => setProducts(res.data))
      .catch(err => console.error(err));
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const handleAddProduct = () => {
    if (!name || !price) return;

    axios.post('http://localhost:4000/products', {
      name,
      price: parseFloat(price)
    }).then(() => {
      setName('');
      setPrice('');
      fetchProducts();
    }).catch(err => console.error(err));
  };

  const handleDeleteProduct = (id: number) => {
    axios.delete(`http://localhost:4000/products/${id}`)
      .then(() => fetchProducts())
      .catch(err => console.error(err));
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Liste des produits</h1>

      <div style={{ marginBottom: '1rem' }}>
        <input
          type="text"
          placeholder="Nom du produit"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <input
          type="number"
          placeholder="Prix"
          value={price}
          onChange={(e) => setPrice(e.target.value)}
          style={{ marginLeft: '1rem' }}
        />
        <button onClick={handleAddProduct} style={{ marginLeft: '1rem' }}>
          Ajouter
        </button>
      </div>

      <ul>
        {products.map((p) => (
          <li key={p.id}>
            {p.name} – {p.price} TND
            <button onClick={() => handleDeleteProduct(p.id)} style={{ marginLeft: '1rem', color: 'red' }}>
              Supprimer
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default App;

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import AdminLayout from './components/Layout/AdminLayout';
import Dashboard from './pages/Dashboard';
import Cars from './pages/Cars';
import Clients from './pages/Clients';
import Settings from './pages/Settings';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Redirect root to admin dashboard */}
          <Route path="/" element={<Navigate to="/admin" replace />} />
          
          {/* Admin routes */}
          <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<Dashboard />} />
            <Route path="cars" element={<Cars />} />
            <Route path="clients" element={<Clients />} />
            <Route path="settings" element={<Settings />} />
            {/* Placeholder routes for future pages */}
            <Route path="rentals" element={<div className="p-6"><h1 className="text-2xl font-bold">Locations - En développement</h1></div>} />
            <Route path="analytics" element={<div className="p-6"><h1 className="text-2xl font-bold">Analytiques - En développement</h1></div>} />
            <Route path="locations" element={<div className="p-6"><h1 className="text-2xl font-bold">Emplacements - En développement</h1></div>} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;

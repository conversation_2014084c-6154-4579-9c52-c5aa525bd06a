import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './product.entity';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
  ) {}

  findAll(): Promise<Product[]> {
    return this.productRepo.find();
  }

  create(product: Partial<Product>): Promise<Product> {
    return this.productRepo.save(product);
  }

  async update(id: number, updateData: Partial<Product>): Promise<Product> {
    const product = await this.productRepo.findOneBy({ id });

    if (!product) {
      throw new NotFoundException(`Produit avec l'id ${id} non trouvé`);
    }

    const updated = Object.assign(product, updateData);
    return this.productRepo.save(updated);
  }

  async remove(id: number): Promise<void> {
    const product = await this.productRepo.findOneBy({ id });

    if (!product) {
      throw new NotFoundException(`Produit avec l'id ${id} non trouvé`);
    }

    await this.productRepo.remove(product);
  }
}

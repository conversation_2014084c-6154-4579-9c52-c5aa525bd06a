{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\essai\\\\mon-projet\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AdminLayout from './components/Layout/AdminLayout';\nimport Dashboard from './pages/Dashboard';\nimport Cars from './pages/Cars';\nimport Clients from './pages/Clients';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/admin\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminLayout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 41\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"cars\",\n            element: /*#__PURE__*/_jsxDEV(Cars, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"clients\",\n            element: /*#__PURE__*/_jsxDEV(Clients, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"settings\",\n            element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"rentals\",\n            element: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Locations - En d\\xE9veloppement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 65\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"analytics\",\n            element: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytiques - En d\\xE9veloppement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 67\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"locations\",\n            element: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Emplacements - En d\\xE9veloppement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 67\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "AdminLayout", "Dashboard", "Cars", "Clients", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/essai/mon-projet/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AdminLayout from './components/Layout/AdminLayout';\nimport Dashboard from './pages/Dashboard';\nimport Cars from './pages/Cars';\nimport Clients from './pages/Clients';\nimport Settings from './pages/Settings';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Routes>\n          {/* Redirect root to admin dashboard */}\n          <Route path=\"/\" element={<Navigate to=\"/admin\" replace />} />\n          \n          {/* Admin routes */}\n          <Route path=\"/admin\" element={<AdminLayout />}>\n            <Route index element={<Dashboard />} />\n            <Route path=\"cars\" element={<Cars />} />\n            <Route path=\"clients\" element={<Clients />} />\n            <Route path=\"settings\" element={<Settings />} />\n            {/* Placeholder routes for future pages */}\n            <Route path=\"rentals\" element={<div className=\"p-6\"><h1 className=\"text-2xl font-bold\">Locations - En développement</h1></div>} />\n            <Route path=\"analytics\" element={<div className=\"p-6\"><h1 className=\"text-2xl font-bold\">Analytiques - En développement</h1></div>} />\n            <Route path=\"locations\" element={<div className=\"p-6\"><h1 className=\"text-2xl font-bold\">Emplacements - En développement</h1></div>} />\n          </Route>\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBF,OAAA,CAACT,MAAM;QAAAW,QAAA,gBAELF,OAAA,CAACR,KAAK;UAACY,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEL,OAAA,CAACP,QAAQ;YAACa,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG7DX,OAAA,CAACR,KAAK;UAACY,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACN,WAAW;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,gBAC5CF,OAAA,CAACR,KAAK;YAACoB,KAAK;YAACP,OAAO,eAAEL,OAAA,CAACL,SAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,eAAEL,OAAA,CAACJ,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEL,OAAA,CAACH,OAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEL,OAAA,CAACF,QAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEhDX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEL,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAD,QAAA,eAACF,OAAA;gBAAIG,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAA4B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClIX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEL,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAD,QAAA,eAACF,OAAA;gBAAIG,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAA8B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtIX,OAAA,CAACR,KAAK;YAACY,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEL,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAD,QAAA,eAACF,OAAA;gBAAIG,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAA+B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACE,EAAA,GAvBQZ,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
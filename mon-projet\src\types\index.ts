// Types pour l'application de location de voiture

export interface Car {
  id: number;
  brand: string;
  model: string;
  year: number;
  price: number;
  category: 'economy' | 'compact' | 'suv' | 'luxury' | 'sport';
  transmission: 'manual' | 'automatic';
  fuel: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  seats: number;
  image: string;
  available: boolean;
  features: string[];
}

export interface Client {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  licenseNumber: string;
  registrationDate: string;
  totalRentals: number;
  status: 'active' | 'inactive' | 'suspended';
}

export interface Rental {
  id: number;
  carId: number;
  clientId: number;
  startDate: string;
  endDate: string;
  totalPrice: number;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  pickupLocation: string;
  returnLocation: string;
}

export interface DashboardStats {
  totalCars: number;
  availableCars: number;
  totalClients: number;
  activeRentals: number;
  monthlyRevenue: number;
  popularCars: Car[];
  recentRentals: Rental[];
}

{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,OAAO;IAClB,IAAI,CAAU;IACd,IAAI,CAAU;IACd,MAAM,GAAW,CAAC,CAAA;IAElB,MAAM,CAAC,MAAM,CAAc,OAAoB,EAAE;QAC/C,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,YAAY,OAAoB,EAAE;QAChC,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAED,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QAChB,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC1D,MAAM,MAAM,CAAC,KAAK,CAAA;QACpB,CAAC;IACH,CAAC;IAED,UAAU,CAAC,IAAa;QACtB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CACb,kDAAkD,CACnD,CAAA;QACH,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAEtB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QACD,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACrB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACrB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QAErB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,IAAa;QACvB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACvB,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,QAAQ,CAAC,IAAa;QACpB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACvB,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,IAAI,CAAC,GAAG,IAAS;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,OAAO,CAAC,GAAG,IAAS;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,GAAG;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;QAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACvB,CAAC;QACD,CAAC,CAAC,IAAI,GAAG,SAAS,CAAA;QAClB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;QAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACvB,CAAC;QACD,CAAC,CAAC,IAAI,GAAG,SAAS,CAAA;QAClB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,OAAO,CACL,EAAkD,EAClD,KAAW;QAEX,KAAK,GAAG,KAAK,IAAI,IAAI,CAAA;QACrB,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;YACrC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;IACH,CAAC;IAED,cAAc,CACZ,EAAkD,EAClD,KAAW;QAEX,KAAK,GAAG,KAAK,IAAI,IAAI,CAAA;QACrB,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChE,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;YACrC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;IACH,CAAC;IAED,GAAG,CAAC,CAAS;QACX,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC,KAAK,CAAA;QACrB,CAAC;IACH,CAAC;IAED,UAAU,CAAC,CAAS;QAClB,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,gDAAgD;YAChD,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC,KAAK,CAAA;QACrB,CAAC;IACH,CAAC;IAED,GAAG,CACD,EAAqC,EACrC,KAAW;QAEX,KAAK,GAAG,KAAK,IAAI,IAAI,CAAA;QACrB,MAAM,GAAG,GAAG,IAAI,OAAO,EAAK,CAAA;QAC5B,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAI,CAAC;YACxC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;YAC5C,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,UAAU,CACR,EAAqC,EACrC,KAAW;QAEX,KAAK,GAAG,KAAK,IAAI,IAAI,CAAA;QACrB,IAAI,GAAG,GAAG,IAAI,OAAO,EAAK,CAAA;QAC1B,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAI,CAAC;YACxC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;YAC5C,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAOD,MAAM,CACJ,EAAqC,EACrC,OAAW;QAEX,IAAI,GAAU,CAAA;QACd,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,GAAG,GAAG,OAAY,CAAA;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;YACvB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,SAAS,CACjB,4CAA4C,CAC7C,CAAA;QACH,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,GAAG,GAAG,EAAE,CAAC,GAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACnC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QAED,OAAO,GAAQ,CAAA;IACjB,CAAC;IAOD,aAAa,CACX,EAAqC,EACrC,OAAW;QAEX,IAAI,GAAU,CAAA;QACd,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,GAAG,GAAG,OAAY,CAAA;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;YACvB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,SAAS,CACjB,4CAA4C,CAC7C,CAAA;QACH,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,GAAG,GAAG,EAAE,CAAC,GAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACnC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QAED,OAAO,GAAQ,CAAA;IACjB,CAAC;IAED,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAA;YACrB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,cAAc;QACZ,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAA;YACrB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,KAAK,CAAC,OAAe,CAAC,EAAE,KAAa,IAAI,CAAC,MAAM;QAC9C,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACX,EAAE,IAAI,IAAI,CAAC,MAAM,CAAA;QACnB,CAAC;QACD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,IAAI,IAAI,IAAI,CAAC,MAAM,CAAA;QACrB,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAA;QACzB,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,CAAA;QACZ,CAAC;QACD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,IAAI,GAAG,CAAC,CAAA;QACV,CAAC;QACD,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACrB,EAAE,GAAG,IAAI,CAAC,MAAM,CAAA;QAClB,CAAC;QACD,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YACrD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACxB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,YAAY,CAAC,OAAe,CAAC,EAAE,KAAa,IAAI,CAAC,MAAM;QACrD,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACX,EAAE,IAAI,IAAI,CAAC,MAAM,CAAA;QACnB,CAAC;QACD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,IAAI,IAAI,IAAI,CAAC,MAAM,CAAA;QACrB,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAA;QACzB,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,CAAA;QACZ,CAAC;QACD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,IAAI,GAAG,CAAC,CAAA;QACV,CAAC;QACD,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACrB,EAAE,GAAG,IAAI,CAAC,MAAM,CAAA;QAClB,CAAC;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;QACnB,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QACD,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YACvD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACxB,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,cAAsB,CAAC,EAAE,GAAG,KAAU;QAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACzB,CAAC;QACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC7B,CAAC;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QAED,MAAM,GAAG,GAAQ,EAAE,CAAA;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YACtB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QAClC,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;YACtB,MAAM,GAAG,WAAW,CAAI,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAA;QAC1C,CAAC;QAED,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,OAAO;QACL,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,KAAK,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YACvD,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAA;YACrB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;YACzB,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAED,mEAAmE;AACnE,SAAS,WAAW,CAClB,IAAgB,EAChB,IAAyB,EACzB,KAAQ;IAER,MAAM,IAAI,GAAG,IAAI,CAAA;IACjB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;IACzC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAI,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAErD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;IACtB,CAAC;IACD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;IACtB,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAA;IAEb,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAS,IAAI,CAAI,IAAgB,EAAE,IAAO;IACxC,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;IACzD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IACD,IAAI,CAAC,MAAM,EAAE,CAAA;AACf,CAAC;AAED,SAAS,OAAO,CAAI,IAAgB,EAAE,IAAO;IAC3C,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAI,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IACD,IAAI,CAAC,MAAM,EAAE,CAAA;AACf,CAAC;AAED,MAAM,OAAO,IAAI;IACf,IAAI,CAAa;IACjB,IAAI,CAAU;IACd,IAAI,CAAU;IACd,KAAK,CAAG;IAER,YACE,KAAQ,EACR,IAA0B,EAC1B,IAA0B,EAC1B,IAA6B;QAE7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAElB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACvB,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACvB,CAAC;IACH,CAAC;CACF", "sourcesContent": ["export class Yallist<T = unknown> {\n  tail?: Node<T>\n  head?: Node<T>\n  length: number = 0\n\n  static create<T = unknown>(list: Iterable<T> = []) {\n    return new Yallist(list)\n  }\n\n  constructor(list: Iterable<T> = []) {\n    for (const item of list) {\n      this.push(item)\n    }\n  }\n\n  *[Symbol.iterator]() {\n    for (let walker = this.head; walker; walker = walker.next) {\n      yield walker.value\n    }\n  }\n\n  removeNode(node: Node<T>) {\n    if (node.list !== this) {\n      throw new Error(\n        'removing node which does not belong to this list',\n      )\n    }\n\n    const next = node.next\n    const prev = node.prev\n\n    if (next) {\n      next.prev = prev\n    }\n\n    if (prev) {\n      prev.next = next\n    }\n\n    if (node === this.head) {\n      this.head = next\n    }\n    if (node === this.tail) {\n      this.tail = prev\n    }\n\n    this.length--\n    node.next = undefined\n    node.prev = undefined\n    node.list = undefined\n\n    return next\n  }\n\n  unshiftNode(node: Node<T>) {\n    if (node === this.head) {\n      return\n    }\n\n    if (node.list) {\n      node.list.removeNode(node)\n    }\n\n    const head = this.head\n    node.list = this\n    node.next = head\n    if (head) {\n      head.prev = node\n    }\n\n    this.head = node\n    if (!this.tail) {\n      this.tail = node\n    }\n    this.length++\n  }\n\n  pushNode(node: Node<T>) {\n    if (node === this.tail) {\n      return\n    }\n\n    if (node.list) {\n      node.list.removeNode(node)\n    }\n\n    const tail = this.tail\n    node.list = this\n    node.prev = tail\n    if (tail) {\n      tail.next = node\n    }\n\n    this.tail = node\n    if (!this.head) {\n      this.head = node\n    }\n    this.length++\n  }\n\n  push(...args: T[]) {\n    for (let i = 0, l = args.length; i < l; i++) {\n      push(this, args[i])\n    }\n    return this.length\n  }\n\n  unshift(...args: T[]) {\n    for (var i = 0, l = args.length; i < l; i++) {\n      unshift(this, args[i])\n    }\n    return this.length\n  }\n\n  pop() {\n    if (!this.tail) {\n      return undefined\n    }\n\n    const res = this.tail.value\n    const t = this.tail\n    this.tail = this.tail.prev\n    if (this.tail) {\n      this.tail.next = undefined\n    } else {\n      this.head = undefined\n    }\n    t.list = undefined\n    this.length--\n    return res\n  }\n\n  shift() {\n    if (!this.head) {\n      return undefined\n    }\n\n    const res = this.head.value\n    const h = this.head\n    this.head = this.head.next\n    if (this.head) {\n      this.head.prev = undefined\n    } else {\n      this.tail = undefined\n    }\n    h.list = undefined\n    this.length--\n    return res\n  }\n\n  forEach(\n    fn: (value: T, i: number, list: Yallist<T>) => any,\n    thisp?: any,\n  ) {\n    thisp = thisp || this\n    for (let walker = this.head, i = 0; !!walker; i++) {\n      fn.call(thisp, walker.value, i, this)\n      walker = walker.next\n    }\n  }\n\n  forEachReverse(\n    fn: (value: T, i: number, list: Yallist<T>) => any,\n    thisp?: any,\n  ) {\n    thisp = thisp || this\n    for (let walker = this.tail, i = this.length - 1; !!walker; i--) {\n      fn.call(thisp, walker.value, i, this)\n      walker = walker.prev\n    }\n  }\n\n  get(n: number) {\n    let i = 0\n    let walker = this.head\n    for (; !!walker && i < n; i++) {\n      walker = walker.next\n    }\n    if (i === n && !!walker) {\n      return walker.value\n    }\n  }\n\n  getReverse(n: number) {\n    let i = 0\n    let walker = this.tail\n    for (; !!walker && i < n; i++) {\n      // abort out of the list early if we hit a cycle\n      walker = walker.prev\n    }\n    if (i === n && !!walker) {\n      return walker.value\n    }\n  }\n\n  map<R = any>(\n    fn: (value: T, list: Yallist<T>) => R,\n    thisp?: any,\n  ): Yallist<R> {\n    thisp = thisp || this\n    const res = new Yallist<R>()\n    for (let walker = this.head; !!walker; ) {\n      res.push(fn.call(thisp, walker.value, this))\n      walker = walker.next\n    }\n    return res\n  }\n\n  mapReverse<R = any>(\n    fn: (value: T, list: Yallist<T>) => R,\n    thisp?: any,\n  ): Yallist<R> {\n    thisp = thisp || this\n    var res = new Yallist<R>()\n    for (let walker = this.tail; !!walker; ) {\n      res.push(fn.call(thisp, walker.value, this))\n      walker = walker.prev\n    }\n    return res\n  }\n\n  reduce(fn: (left: T, right: T, i: number) => T): T\n  reduce<R = any>(\n    fn: (acc: R, next: T, i: number) => R,\n    initial: R,\n  ): R\n  reduce<R = any>(\n    fn: (acc: R, next: T, i: number) => R,\n    initial?: R,\n  ): R {\n    let acc: R | T\n    let walker = this.head\n    if (arguments.length > 1) {\n      acc = initial as R\n    } else if (this.head) {\n      walker = this.head.next\n      acc = this.head.value\n    } else {\n      throw new TypeError(\n        'Reduce of empty list with no initial value',\n      )\n    }\n\n    for (var i = 0; !!walker; i++) {\n      acc = fn(acc as R, walker.value, i)\n      walker = walker.next\n    }\n\n    return acc as R\n  }\n\n  reduceReverse(fn: (left: T, right: T, i: number) => T): T\n  reduceReverse<R = any>(\n    fn: (acc: R, next: T, i: number) => R,\n    initial: R,\n  ): R\n  reduceReverse<R = any>(\n    fn: (acc: R, next: T, i: number) => R,\n    initial?: R,\n  ): R {\n    let acc: R | T\n    let walker = this.tail\n    if (arguments.length > 1) {\n      acc = initial as R\n    } else if (this.tail) {\n      walker = this.tail.prev\n      acc = this.tail.value\n    } else {\n      throw new TypeError(\n        'Reduce of empty list with no initial value',\n      )\n    }\n\n    for (let i = this.length - 1; !!walker; i--) {\n      acc = fn(acc as R, walker.value, i)\n      walker = walker.prev\n    }\n\n    return acc as R\n  }\n\n  toArray() {\n    const arr = new Array(this.length)\n    for (let i = 0, walker = this.head; !!walker; i++) {\n      arr[i] = walker.value\n      walker = walker.next\n    }\n    return arr\n  }\n\n  toArrayReverse() {\n    const arr = new Array(this.length)\n    for (let i = 0, walker = this.tail; !!walker; i++) {\n      arr[i] = walker.value\n      walker = walker.prev\n    }\n    return arr\n  }\n\n  slice(from: number = 0, to: number = this.length) {\n    if (to < 0) {\n      to += this.length\n    }\n    if (from < 0) {\n      from += this.length\n    }\n    const ret = new Yallist()\n    if (to < from || to < 0) {\n      return ret\n    }\n    if (from < 0) {\n      from = 0\n    }\n    if (to > this.length) {\n      to = this.length\n    }\n    let walker = this.head\n    let i = 0\n    for (i = 0; !!walker && i < from; i++) {\n      walker = walker.next\n    }\n    for (; !!walker && i < to; i++, walker = walker.next) {\n      ret.push(walker.value)\n    }\n    return ret\n  }\n\n  sliceReverse(from: number = 0, to: number = this.length) {\n    if (to < 0) {\n      to += this.length\n    }\n    if (from < 0) {\n      from += this.length\n    }\n    const ret = new Yallist()\n    if (to < from || to < 0) {\n      return ret\n    }\n    if (from < 0) {\n      from = 0\n    }\n    if (to > this.length) {\n      to = this.length\n    }\n    let i = this.length\n    let walker = this.tail\n    for (; !!walker && i > to; i--) {\n      walker = walker.prev\n    }\n    for (; !!walker && i > from; i--, walker = walker.prev) {\n      ret.push(walker.value)\n    }\n    return ret\n  }\n\n  splice(start: number, deleteCount: number = 0, ...nodes: T[]) {\n    if (start > this.length) {\n      start = this.length - 1\n    }\n    if (start < 0) {\n      start = this.length + start\n    }\n\n    let walker = this.head\n\n    for (let i = 0; !!walker && i < start; i++) {\n      walker = walker.next\n    }\n\n    const ret: T[] = []\n    for (let i = 0; !!walker && i < deleteCount; i++) {\n      ret.push(walker.value)\n      walker = this.removeNode(walker)\n    }\n    if (!walker) {\n      walker = this.tail\n    } else if (walker !== this.tail) {\n      walker = walker.prev\n    }\n\n    for (const v of nodes) {\n      walker = insertAfter<T>(this, walker, v)\n    }\n\n    return ret\n  }\n\n  reverse() {\n    const head = this.head\n    const tail = this.tail\n    for (let walker = head; !!walker; walker = walker.prev) {\n      const p = walker.prev\n      walker.prev = walker.next\n      walker.next = p\n    }\n    this.head = tail\n    this.tail = head\n    return this\n  }\n}\n\n// insertAfter undefined means \"make the node the new head of list\"\nfunction insertAfter<T>(\n  self: Yallist<T>,\n  node: Node<T> | undefined,\n  value: T,\n) {\n  const prev = node\n  const next = node ? node.next : self.head\n  const inserted = new Node<T>(value, prev, next, self)\n\n  if (inserted.next === undefined) {\n    self.tail = inserted\n  }\n  if (inserted.prev === undefined) {\n    self.head = inserted\n  }\n\n  self.length++\n\n  return inserted\n}\n\nfunction push<T>(self: Yallist<T>, item: T) {\n  self.tail = new Node<T>(item, self.tail, undefined, self)\n  if (!self.head) {\n    self.head = self.tail\n  }\n  self.length++\n}\n\nfunction unshift<T>(self: Yallist<T>, item: T) {\n  self.head = new Node<T>(item, undefined, self.head, self)\n  if (!self.tail) {\n    self.tail = self.head\n  }\n  self.length++\n}\n\nexport class Node<T = unknown> {\n  list?: Yallist<T>\n  next?: Node<T>\n  prev?: Node<T>\n  value: T\n\n  constructor(\n    value: T,\n    prev?: Node<T> | undefined,\n    next?: Node<T> | undefined,\n    list?: Yallist<T> | undefined,\n  ) {\n    this.list = list\n    this.value = value\n\n    if (prev) {\n      prev.next = this\n      this.prev = prev\n    } else {\n      this.prev = undefined\n    }\n\n    if (next) {\n      next.prev = this\n      this.next = next\n    } else {\n      this.next = undefined\n    }\n  }\n}\n"]}
import React, { useState } from 'react';
import { Plus, Search, Filter, Grid, List } from 'lucide-react';
import { Car } from '../types';
import CarCard from '../components/Cars/CarCard';
import CarForm from '../components/Cars/CarForm';

const Cars: React.FC = () => {
  const [cars, setCars] = useState<Car[]>([
    {
      id: 1,
      brand: 'Toyota',
      model: 'Corolla',
      year: 2023,
      price: 45,
      category: 'economy',
      transmission: 'automatic',
      fuel: 'gasoline',
      seats: 5,
      image: 'https://via.placeholder.com/400x300?text=Toyota+Corolla',
      available: true,
      features: ['Climatisation', 'Bluetooth', 'GPS']
    },
    {
      id: 2,
      brand: 'BMW',
      model: 'X3',
      year: 2022,
      price: 120,
      category: 'suv',
      transmission: 'automatic',
      fuel: 'diesel',
      seats: 5,
      image: 'https://via.placeholder.com/400x300?text=BMW+X3',
      available: false,
      features: ['Cuir', 'Toit ouvrant', 'Caméra de recul', 'Sièges chauffants']
    },
    {
      id: 3,
      brand: 'Mercedes',
      model: 'C-Class',
      year: 2023,
      price: 95,
      category: 'luxury',
      transmission: 'automatic',
      fuel: 'gasoline',
      seats: 5,
      image: 'https://via.placeholder.com/400x300?text=Mercedes+C-Class',
      available: true,
      features: ['Cuir', 'GPS', 'Bluetooth', 'Climatisation automatique']
    },
    {
      id: 4,
      brand: 'Audi',
      model: 'A4',
      year: 2022,
      price: 85,
      category: 'compact',
      transmission: 'manual',
      fuel: 'diesel',
      seats: 5,
      image: 'https://via.placeholder.com/400x300?text=Audi+A4',
      available: true,
      features: ['GPS', 'Bluetooth', 'Régulateur de vitesse']
    }
  ]);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCar, setEditingCar] = useState<Car | undefined>();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredCars = cars.filter(car => {
    const matchesSearch = car.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         car.model.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || car.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAddCar = () => {
    setEditingCar(undefined);
    setIsFormOpen(true);
  };

  const handleEditCar = (car: Car) => {
    setEditingCar(car);
    setIsFormOpen(true);
  };

  const handleDeleteCar = (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette voiture ?')) {
      setCars(prev => prev.filter(car => car.id !== id));
    }
  };

  const handleViewCar = (car: Car) => {
    // Implement view logic
    console.log('Viewing car:', car);
  };

  const handleSubmitCar = (carData: Omit<Car, 'id'>) => {
    if (editingCar) {
      // Update existing car
      setCars(prev => prev.map(car => 
        car.id === editingCar.id ? { ...carData, id: editingCar.id } : car
      ));
    } else {
      // Add new car
      const newCar: Car = {
        ...carData,
        id: Math.max(...cars.map(c => c.id), 0) + 1
      };
      setCars(prev => [...prev, newCar]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Voitures</h1>
          <p className="text-gray-600 mt-1">Gérez votre flotte de véhicules</p>
        </div>
        <button
          onClick={handleAddCar}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>Ajouter une voiture</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Search */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher par marque ou modèle..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Category Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Toutes catégories</option>
                <option value="economy">Économique</option>
                <option value="compact">Compact</option>
                <option value="suv">SUV</option>
                <option value="luxury">Luxe</option>
                <option value="sport">Sport</option>
              </select>
            </div>

            {/* View Mode */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid' ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list' ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total</p>
              <p className="text-2xl font-bold text-gray-900">{cars.length}</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 font-semibold">{cars.length}</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Disponibles</p>
              <p className="text-2xl font-bold text-green-600">{cars.filter(c => c.available).length}</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <span className="text-green-600 font-semibold">{cars.filter(c => c.available).length}</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Louées</p>
              <p className="text-2xl font-bold text-orange-600">{cars.filter(c => !c.available).length}</p>
            </div>
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <span className="text-orange-600 font-semibold">{cars.filter(c => !c.available).length}</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Prix moyen</p>
              <p className="text-2xl font-bold text-purple-600">
                {Math.round(cars.reduce((sum, car) => sum + car.price, 0) / cars.length)} TND
              </p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <span className="text-purple-600 font-semibold">€</span>
            </div>
          </div>
        </div>
      </div>

      {/* Cars Grid/List */}
      <div className={`${
        viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
          : 'space-y-4'
      }`}>
        {filteredCars.map(car => (
          <CarCard
            key={car.id}
            car={car}
            onEdit={handleEditCar}
            onDelete={handleDeleteCar}
            onView={handleViewCar}
          />
        ))}
      </div>

      {filteredCars.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune voiture trouvée</h3>
          <p className="text-gray-600">Essayez de modifier vos critères de recherche.</p>
        </div>
      )}

      {/* Car Form Modal */}
      <CarForm
        car={editingCar}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleSubmitCar}
      />
    </div>
  );
};

export default Cars;
